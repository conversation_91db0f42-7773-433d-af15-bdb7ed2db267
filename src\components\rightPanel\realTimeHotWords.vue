<!-- 电力系统  -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <span>电力系统</span>
        <div class="dropdown">
          <span>电压</span>
          <span class="dropdown-icon">▼</span>
        </div>
      </div>
    </template>
    <template #content>
      <div class="power-system">
        <!-- 当前总有功电能显示 -->
        <div class="power-info">
          <div class="power-icon">
            <img src="@/assets/img/水泵icon.png" alt="水泵图标" />
          </div>
          <div class="power-text">
            <div class="power-label">当前总有功电能</div>
            <div class="power-value">
              <span class="value">211.4</span>
              <span class="unit">kw/h</span>
            </div>
          </div>
        </div>
        <!-- 图表区域 -->
        <div class="chart-container">
          <CEcharts :option="option" />
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref<any>({})

const initEcharts = () => {
  // 模拟电力系统三相电压数据
  const timeData = ['00:00', '04:00', '06:00', '08:00', '10:00', '12:00', '14:00', '18:00', '20:00']
  const aPhaseData = [10, 15, 25, 35, 45, 55, 70, 45, 25]
  const bPhaseData = [45, 55, 50, 60, 55, 40, 35, 50, 45]
  const cPhaseData = [25, 35, 45, 50, 35, 25, 20, 30, 15]

  const options: any = {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '0',
      right: '0',
      bottom: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 255, 255, 0.3)'
        }
      },
      axisLabel: {
        color: 'rgba(0, 255, 255, 0.8)',
        fontSize: 10
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 80,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: 'rgba(0, 255, 255, 0.8)',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    legend: {
      data: ['A相', 'B相', 'C相'],
      bottom: '5%',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      itemWidth: 12,
      itemHeight: 8
    },
    series: [
      {
        name: 'A相',
        type: 'line',
        data: aPhaseData,
        smooth: true,
        lineStyle: {
          color: '#00FFFF',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 255, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: 'B相',
        type: 'line',
        data: bPhaseData,
        smooth: true,
        lineStyle: {
          color: '#FFD700',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(255, 215, 0, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(255, 215, 0, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      },
      {
        name: 'C相',
        type: 'line',
        data: cPhaseData,
        smooth: true,
        lineStyle: {
          color: '#00FF7F',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(0, 255, 127, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(0, 255, 127, 0.05)'
              }
            ]
          }
        },
        symbol: 'none'
      }
    ],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'rgba(0, 255, 255, 0.5)',
      textStyle: {
        color: '#fff'
      }
    }
  }
  return options
}

onMounted(() => {
  option.value = initEcharts()
})
</script>

<style lang="scss" scoped>
::v-deep .panel-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .dropdown {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: rgba(0, 255, 255, 0.8);
    cursor: pointer;

    .dropdown-icon {
      font-size: 12px;
      transition: transform 0.3s ease;
    }

    &:hover .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}

.power-system {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // gap: 12px;
}

.power-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 7px 16px;
  background: url('@/assets/images/dy_title.png') no-repeat center center;
  background-size: 100% 100%;
  border-radius: 8px;

  .power-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 32px;
      height: 32px;
      object-fit: contain;
    }
  }

  .power-text {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 4px;

    .power-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
    }

    .power-value {
      display: flex;
      align-items: baseline;
      gap: 4px;

      .value {
        font-size: 24px;
        font-weight: bold;
        color: #00FFFF;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      .unit {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

.chart-container {
  flex: 1;
  width: 100%;
  min-height: 180px;
}
</style>
